import PropTypes from "prop-types";
import { useEffect, useRef, useState, useMemo } from "react";
import ForceGraph2D from "react-force-graph-2d";
import { forceCollide } from "d3-force-3d";
import use360requestStore from "store/360requestStore.js";
import { toPersianNumber } from "utils/helper";

const GraphChart = ({
  data = [],
  onLinkSelect,
  onCenterNodeSelect,
  onNodeSelect,
  user,
  isReverse,
  selectedListNode,
}) => {
  const fgRef = useRef();
  const imageCache = useRef({});
  const [hoveredLink, setHoveredLink] = useState(null);

  const sourceReport = use360requestStore((state) => ({
    profile: state.report?.content?.source_info,
  }));

  const profile = sourceReport.profile;

  // const report = useReport360Store.getState().report || {};
  // const profile = report.profile || {};
  const avatar = profile.avatar || user;
  const name = profile.user_title || "Unknown User";

  const { nodes, links } = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0)
      return { nodes: [], links: [] };

    const nodesMap = new Map();
    const linksArray = [];

    data.forEach(
      ({
        from,
        to,
        count,
        avatar: nodeAvatar,
        content_query,
        profile_info,
      }) => {
        const fromNode = from || name;
        const toNode = to || "Unknown";

        if (!nodesMap.has(fromNode)) {
          nodesMap.set(fromNode, {
            id: fromNode,
            avatar: fromNode === name ? avatar : nodeAvatar || user,
            level: fromNode === name ? 0 : 1,
            count: count || 1,
          });
        }
        if (!nodesMap.has(toNode)) {
          nodesMap.set(toNode, {
            id: toNode,
            avatar: nodeAvatar || user,
            level: 1,
            count: count || 1,
            content_query: content_query,
            profile_info: profile_info,
          });
        }

        linksArray.push({
          source: fromNode,
          target: toNode,
          value: count || 1,
          count: count || 1,
          content_query: content_query,
          profile_info: profile_info,
        });
      }
    );

    return { nodes: Array.from(nodesMap.values()), links: linksArray };
  }, [data, name, avatar]);

  useEffect(() => {
    if (fgRef.current) {
      fgRef.current.d3Force("collision", forceCollide(8));
    }
  }, [nodes]);

  useEffect(() => {
    if (fgRef.current) {
      if (hoveredLink) {
        fgRef.current.d3Force("link").strength(0);
        fgRef.current.d3Force("charge").strength(0);
        fgRef.current.d3Force("collision").strength(0);
      } else {
        fgRef.current.d3Force("link").strength(1);
        fgRef.current.d3Force("charge").strength(-30);
        fgRef.current.d3Force("collision").strength(1);
      }
    }
  }, [hoveredLink]);

  const isLinkSelected = (link) => {
    if (!selectedListNode) return false;
    return (
      link.content_query === selectedListNode.content_query &&
      link.profile_info?.user_name ===
        selectedListNode.profile_info?.user_name &&
      link.count === selectedListNode.count
    );
  };

  const isNodeSelected = (node) => {
    if (!selectedListNode || node.id === name) return false;
    return (
      node.content_query === selectedListNode.content_query &&
      node.profile_info?.user_name === selectedListNode.profile_info?.user_name
    );
  };

  // useEffect(() => {
  //   const handleResize = () => {
  //     if (containerRef.current) {
  //       const { width, height } = containerRef.current.getBoundingClientRect();

  //       console.log(containerRef);
  //       console.log({ width, height });
  //       setDimensions({ width: 1080, height: 500 });
  //     }
  //   };
  //   handleResize();
  //   window.addEventListener("resize", handleResize);
  //   return () => window.removeEventListener("resize", handleResize);
  // }, []);

  return (
    <div
      style={{
        width: "100%",
        height: "500px",
        position: "relative",
        borderRadius: "12px",
        overflow: "hidden",
        transition: "all 0.3s ease-in-out",
      }}
    >
      <ForceGraph2D
        ref={fgRef}
        graphData={{ nodes, links }}
        backgroundColor="transparent"
        linkColor={(link) =>
          isLinkSelected(link) || hoveredLink === link
            ? "rgba(102, 153, 255, 1)"
            : "rgba(170, 170, 170, 0.7)"
        }
        linkWidth={1}
        nodeRelSize={3}
        nodeId="id"
        nodeLabel={(node) => node.id}
        nodeCanvasObject={(node, ctx, globalScale) => {
          const defaultSize = Math.max(8, 3 + Math.sqrt(node.count) * 3);
          const maxNodeSize = 15;
          const size = Math.min(
            node.id === name ? defaultSize * 1.5 : defaultSize,
            maxNodeSize
          );

          if (!imageCache.current[node.avatar]) {
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = node.avatar;
            img.onload = () => {
              imageCache.current[node.avatar] = img;
            };
            img.onerror = () => {
              console.warn(`Failed to load image: ${node.avatar}`);
              img.src = user;
            };
            imageCache.current[node.avatar] = img;
          }

          const img = imageCache.current[node.avatar];
          if (img.complete) {
            ctx.save();
            ctx.beginPath();
            ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI, false);
            ctx.clip();
            try {
              ctx.drawImage(
                img,
                node.x - size / 2,
                node.y - size / 2,
                size,
                size
              );
            } catch (error) {
              console.error(`Error drawing image for node ${node.id}:`, error);
            }

            if (node.id === name) {
              ctx.restore();
              ctx.beginPath();
              ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI);
              ctx.shadowBlur = 10;
              ctx.shadowColor = "rgba(102, 153, 255, 1)";
              ctx.strokeStyle = "rgba(102, 153, 255, 1)";
              ctx.lineWidth = 1;
              ctx.stroke();
              ctx.shadowBlur = 0;
            } else if (isNodeSelected(node)) {
              ctx.restore();
              ctx.beginPath();
              ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI);
              ctx.strokeStyle = "rgba(102, 153, 255, 1)";
              ctx.lineWidth = 1;
              ctx.stroke();
            } else {
              ctx.restore();
              ctx.beginPath();
              ctx.arc(node.x, node.y, size / 2, 0, 2 * Math.PI);
              ctx.strokeStyle = "#333333";
              ctx.lineWidth = 0.1;
              ctx.stroke();
            }
            ctx.restore();
          }
        }}
        linkCanvasObjectMode={() => "after"}
        linkCanvasObject={(link, ctx) => {
          const midX = (link.source.x + link.target.x) / 2;
          const midY = (link.source.y + link.target.y) / 2;
          const dx = link.target.x - link.source.x;
          const dy = link.target.y - link.source.y;
          const angle = Math.atan2(dy, dx);
          const offsetY = -2;

          const isHovered = hoveredLink === link;
          const isSelected = isLinkSelected(link);

          ctx.save();
          ctx.translate(midX, midY);
          ctx.rotate(angle);
          ctx.font =
            isHovered || isSelected ? "4px IranYekan" : "3px IranYekan";
          ctx.fillStyle =
            isHovered || isSelected ? "rgba(102, 153, 255, 1)" : "black";
          ctx.shadowBlur = isHovered || isSelected ? 5 : 0;
          ctx.shadowColor = "rgba(255, 111, 97, 0.5)";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText(toPersianNumber(link.count), 0, offsetY);
          ctx.shadowBlur = 0;
          ctx.restore();

          const arrowSize = 2;
          const arrowAngle = Math.PI / 4;

          ctx.save();
          ctx.strokeStyle =
            isHovered || isSelected
              ? "rgba(61, 126, 255, 0.8)"
              : "rgba(170, 170, 170, 0.7)";
          ctx.lineWidth = 0.7;
          ctx.fillStyle =
            isHovered || isSelected
              ? "rgba(61, 126, 255, 0.8)"
              : "rgba(170, 170, 170, 0.7)";

          const offset = 0.25;
          const arrowPositionX = midX + offset * dx;
          const arrowPositionY = midY + offset * dy;

          ctx.beginPath();
          ctx.moveTo(arrowPositionX, arrowPositionY);
          if (isReverse) {
            ctx.lineTo(
              arrowPositionX + arrowSize * Math.cos(angle - arrowAngle),
              arrowPositionY + arrowSize * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(arrowPositionX, arrowPositionY);
            ctx.lineTo(
              arrowPositionX + arrowSize * Math.cos(angle + arrowAngle),
              arrowPositionY + arrowSize * Math.sin(angle + arrowAngle)
            );
          } else {
            ctx.lineTo(
              arrowPositionX - arrowSize * Math.cos(angle - arrowAngle),
              arrowPositionY - arrowSize * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(arrowPositionX, arrowPositionY);
            ctx.lineTo(
              arrowPositionX - arrowSize * Math.cos(angle + arrowAngle),
              arrowPositionY - arrowSize * Math.sin(angle + arrowAngle)
            );
          }

          ctx.stroke();
          ctx.restore();
        }}
        d3VelocityDecay={0.3}
        width={undefined}
        height={undefined}
        zoomToFit
        maxZoom={4.8}
        minZoom={4}
        d3Force="link"
        onNodeClick={(node) => {
          if (node.id === name) {
            onCenterNodeSelect();
          } else {
            onNodeSelect(node?.content_query, node?.profile_info, node);
          }
        }}
        onLinkHover={(link) => {
          setHoveredLink(link);
        }}
        onLinkClick={(link) => {
          const targetNode = link.target;
          if (targetNode.id === name) return;
          if (onLinkSelect) {
            onLinkSelect(
              targetNode.content_query,
              targetNode.profile_info,
              targetNode.count
            );
          }
        }}
      />
    </div>
  );
};

GraphChart.propTypes = {
  ref: PropTypes.object,
  data: PropTypes.array,
  onLinkSelect: PropTypes.func,
  onCenterNodeSelect: PropTypes.func,
  onNodeSelect: PropTypes.func,
  user: PropTypes.string,
  isReverse: PropTypes.bool,
  selectedListNode: PropTypes.object,
};

export default GraphChart;
